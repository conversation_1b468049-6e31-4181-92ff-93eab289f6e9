import nibabel as nib
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.colors import ListedColormap
import os
from pathlib import Path
import argparse

class NIfTIToPNGExporter:
    def __init__(self, nifti_path, output_dir, prefix="slice", image_type="auto", export_uint8=False):
        """
        Exporte toutes les slices d'un volume NIfTI en images PNG

        Args:
            nifti_path: Chemin vers le fichier NIfTI
            output_dir: Dossier de sortie pour les images PNG
            prefix: Préfixe pour les noms de fichiers (défaut: "slice")
            image_type: Type d'images ("auto", "imagesTr", "labelsTr")
            export_uint8: Si True, export en uint8, sinon RGB pour imagesTr ou couleurs distinctes pour labelsTr
        """
        self.nifti_path = nifti_path
        self.output_dir = Path(output_dir)
        self.prefix = prefix
        self.image_type = image_type
        self.export_uint8 = export_uint8
        
        # Créer le dossier de sortie s'il n'existe pas
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Charger le volume
        print(f"📂 Chargement du fichier NIfTI : {nifti_path}")
        self.volume_raw = nib.load(nifti_path).get_fdata()

        # Déterminer les dimensions et résolution par défaut
        self.original_shape = self.volume_raw.shape
        print(f"📊 Volume chargé : {self.original_shape} (Z, Y, X)")
        print(f"📈 Valeurs uniques : {np.unique(self.volume_raw)}")

        # Détection automatique du type d'images
        unique_values = np.unique(self.volume_raw)
        self.detected_type = self._detect_image_type(unique_values)

        # Utiliser le type spécifié ou celui détecté
        if self.image_type == "auto":
            self.final_image_type = self.detected_type
        else:
            self.final_image_type = self.image_type

        print(f"🏷️ Type d'images : {self.final_image_type}")
        print(f"🎨 Mode export : {'uint8' if self.export_uint8 else 'RGB/Couleurs'}")

        # Préparer les données selon le type et le mode d'export
        self._prepare_data()

        # Configuration du colormap
        self._setup_colormap()
        
    def _detect_image_type(self, unique_values):
        """Détecte si le volume contient des images (imagesTr) ou des labels (labelsTr)"""
        # Critères pour détecter des labels :
        # 1. Valeurs entières uniquement
        # 2. Valeurs dans une plage limitée (typiquement 0-4 pour les classes)
        # 3. Nombre limité de valeurs uniques

        # Vérifier si toutes les valeurs sont des entiers
        if not np.allclose(unique_values, unique_values.astype(int)):
            return "imagesTr"  # Valeurs continues -> images

        # Vérifier si les valeurs correspondent à des classes de segmentation
        if unique_values.max() <= 10 and unique_values.min() >= 0:
            if len(unique_values) <= 15:  # Nombre raisonnable de classes
                return "labelsTr"

        return "imagesTr"  # Par défaut, considérer comme des images

    def _prepare_data(self):
        """Prépare les données selon le type d'images et le mode d'export"""
        if self.final_image_type == "labelsTr":
            # Pour les labels, garder les valeurs exactes
            self.volume = self.volume_raw.astype(np.uint8)
        else:
            # Pour les images, normaliser vers 0-255
            if self.volume_raw.dtype in [np.float32, np.float64]:
                # Normaliser les données float
                vmin, vmax = self.volume_raw.min(), self.volume_raw.max()
                if vmax > vmin:
                    normalized = (self.volume_raw - vmin) / (vmax - vmin)
                    self.volume = (normalized * 255).astype(np.uint8)
                else:
                    self.volume = np.zeros_like(self.volume_raw, dtype=np.uint8)
            else:
                # Données entières, normaliser selon la plage
                vmin, vmax = self.volume_raw.min(), self.volume_raw.max()
                if vmax > 255:
                    normalized = (self.volume_raw - vmin) / (vmax - vmin)
                    self.volume = (normalized * 255).astype(np.uint8)
                else:
                    self.volume = self.volume_raw.astype(np.uint8)
    
    def _setup_colormap(self):
        """Configure le colormap selon le type d'images et le mode d'export"""
        if self.final_image_type == "labelsTr":
            if self.export_uint8:
                print("🎨 Labels : Export direct des classes (uint8)")
                self.cmap = None  # Pas de colormap, export direct
            else:
                print("🎨 Labels : Export avec couleurs distinctes pour visualisation")
                # Colormap avec couleurs distinctes pour les classes
                self.cmap = ListedColormap([
                    (0, 0, 0),        # 0: background - noir
                    (0, 0, 1),        # 1: classe 1 - bleu
                    (0, 1, 0),        # 2: classe 2 - vert
                    (1, 0, 0),        # 3: classe 3 - rouge
                    (1, 1, 0),        # 4: classe 4 - jaune
                    (1, 0, 1),        # 5: classe 5 - magenta
                    (0, 1, 1),        # 6: classe 6 - cyan
                    (0.5, 0.5, 0.5),  # 7: classe 7 - gris
                    (1, 0.5, 0),      # 8: classe 8 - orange
                    (0.5, 0, 0.5),    # 9: classe 9 - violet
                ])
        else:  # imagesTr
            if self.export_uint8:
                print("🎨 Images : Export en niveaux de gris (uint8)")
                self.cmap = 'gray'
            else:
                print("🎨 Images : Export en RGB avec colormap")
                self.cmap = 'viridis'  # Colormap par défaut pour les images

        # Définir les limites de valeurs
        self.vmin, self.vmax = 0, 255
    
    def export_all_slices(self, dpi=150, figsize=(8, 6)):
        """
        Exporte toutes les slices en images PNG
        
        Args:
            dpi: Résolution des images (défaut: 150)
            figsize: Taille de la figure (défaut: (8, 6))
        """
        num_slices = self.volume.shape[0]
        print(f"🚀 Début de l'export de {num_slices} slices...")
        
        for slice_idx in range(num_slices):
            self._export_slice(slice_idx, dpi, figsize)
            
            # Afficher le progrès
            if (slice_idx + 1) % 10 == 0 or slice_idx == num_slices - 1:
                progress = ((slice_idx + 1) / num_slices) * 100
                print(f"📈 Progrès : {slice_idx + 1}/{num_slices} ({progress:.1f}%)")
        
        print(f"✅ Export terminé ! {num_slices} images sauvegardées dans : {self.output_dir}")
        
    def _export_slice(self, slice_idx, dpi, figsize):
        """Exporte une slice spécifique selon le mode choisi"""
        current_data = self.volume[slice_idx, :, :]

        # Créer le nom de fichier avec sérialisation simple à 4 chiffres
        filename = f"{slice_idx + 1:04d}.png"
        filepath = self.output_dir / filename

        if self.final_image_type == "labelsTr" and self.export_uint8:
            # Export direct des classes sans colormap
            from PIL import Image
            img = Image.fromarray(current_data, mode='L')
            img.save(filepath)
        elif not self.export_uint8 and self.cmap is not None:
            # Export RGB avec colormap
            if hasattr(self.cmap, '__call__'):
                # Colormap matplotlib
                if self.final_image_type == "labelsTr":
                    # Pour les labels, normaliser selon le nombre de classes
                    max_class = current_data.max()
                    if max_class > 0:
                        normalized_data = current_data / max_class
                    else:
                        normalized_data = current_data
                else:
                    # Pour les images, normaliser vers 0-1
                    normalized_data = current_data / 255.0

                colored_data = self.cmap(normalized_data)
                rgb_data = (colored_data[:, :, :3] * 255).astype(np.uint8)
                from PIL import Image
                img = Image.fromarray(rgb_data, mode='RGB')
                img.save(filepath)
            else:
                # Fallback : convertir en RGB grayscale
                from PIL import Image
                rgb_data = np.stack([current_data] * 3, axis=-1)
                img = Image.fromarray(rgb_data, mode='RGB')
                img.save(filepath)
        else:
            # Export uint8 en grayscale
            from PIL import Image
            img = Image.fromarray(current_data, mode='L')
            img.save(filepath)

    def _export_with_matplotlib(self, data, filepath, dpi, figsize):
        """Export avec matplotlib (fallback)"""
        # Calculer figsize basé sur les dimensions réelles des données
        height, width = data.shape
        # Utiliser les dimensions réelles en pouces pour préserver les proportions
        fig_width = width / dpi
        fig_height = height / dpi

        fig, ax = plt.subplots(figsize=(fig_width, fig_height))
        ax.imshow(data, cmap=self.cmap, vmin=self.vmin, vmax=self.vmax)
        ax.set_xticks([])
        ax.set_yticks([])
        ax.axis('off')
        plt.savefig(filepath, dpi=dpi, bbox_inches='tight', pad_inches=0, facecolor='white')
        plt.close(fig)
    
    def export_slice_range(self, start_slice, end_slice, dpi=150, figsize=(8, 6)):
        """
        Exporte une plage de slices
        
        Args:
            start_slice: Index de début (inclus)
            end_slice: Index de fin (inclus)
            dpi: Résolution des images
            figsize: Taille de la figure
        """
        num_slices = self.volume.shape[0]
        start_slice = max(0, start_slice)
        end_slice = min(num_slices - 1, end_slice)
        
        print(f"🚀 Export des slices {start_slice} à {end_slice}...")
        
        for slice_idx in range(start_slice, end_slice + 1):
            self._export_slice(slice_idx, dpi, figsize)
        
        exported_count = end_slice - start_slice + 1
        print(f"✅ Export terminé ! {exported_count} images sauvegardées dans : {self.output_dir}")
    
    def get_slice_info(self, slice_idx):
        """Retourne des informations sur une slice spécifique"""
        if slice_idx < 0 or slice_idx >= self.volume.shape[0]:
            return None
        
        current_data = self.volume[slice_idx, :, :]
        unique_values, counts = np.unique(current_data, return_counts=True)
        
        info = {
            'slice_index': slice_idx,
            'shape': current_data.shape,
            'min_value': current_data.min(),
            'max_value': current_data.max(),
            'unique_values': unique_values,
            'value_counts': counts,
            'is_mask': self.is_mask
        }
        
        return info

def main():
    """Fonction principale avec interface en ligne de commande"""
    parser = argparse.ArgumentParser(description="Exporte les slices d'un volume NIfTI en images PNG")
    parser.add_argument("input_file", help="Chemin vers le fichier NIfTI")
    parser.add_argument("output_dir", help="Dossier de sortie pour les images PNG")
    parser.add_argument("--prefix", default="slice", help="Préfixe pour les noms de fichiers (défaut: slice)")
    parser.add_argument("--image-type", choices=["auto", "imagesTr", "labelsTr"], default="auto",
                       help="Type d'images (défaut: auto)")
    parser.add_argument("--uint8", action="store_true", help="Export en uint8 (sinon RGB/couleurs)")
    parser.add_argument("--dpi", type=int, help="Résolution des images (défaut: résolution du volume)")
    parser.add_argument("--start", type=int, help="Index de début pour export partiel")
    parser.add_argument("--end", type=int, help="Index de fin pour export partiel")
    
    args = parser.parse_args()
    
    # Vérifier que le fichier d'entrée existe
    if not os.path.exists(args.input_file):
        print(f"❌ Erreur : Le fichier {args.input_file} n'existe pas")
        return
    
    try:
        # Créer le dossier de sortie basé sur le nom du fichier d'entrée
        input_path = Path(args.input_file)
        volume_name = input_path.stem.replace('.nii', '')  # Enlever .nii.gz ou .nii
        output_dir = Path(args.output_dir) / volume_name

        # Déterminer la résolution par défaut
        if args.dpi is None:
            # Utiliser la résolution du volume (approximation basée sur les dimensions)
            temp_volume = nib.load(args.input_file).get_fdata()
            # Calculer DPI basé sur les dimensions (heuristique)
            avg_dim = (temp_volume.shape[1] + temp_volume.shape[2]) / 2
            if avg_dim > 512:
                default_dpi = 300
            elif avg_dim > 256:
                default_dpi = 200
            else:
                default_dpi = 150
        else:
            default_dpi = args.dpi

        # Créer l'exporteur
        exporter = NIfTIToPNGExporter(
            nifti_path=args.input_file,
            output_dir=str(output_dir),
            prefix=args.prefix,
            image_type=args.image_type,
            export_uint8=args.uint8
        )
        
        # Exporter selon les paramètres
        if args.start is not None and args.end is not None:
            exporter.export_slice_range(args.start, args.end, dpi=default_dpi)
        else:
            exporter.export_all_slices(dpi=default_dpi)
            
    except Exception as e:
        print(f"❌ Erreur lors de l'export : {str(e)}")


if __name__ == "__main__":
    # Si exécuté directement, utiliser les paramètres par défaut ou l'interface CLI
    import sys
    
    if len(sys.argv) == 1:
        # Mode interactif simple
        print("=== Exporteur NIfTI vers PNG ===")
        nifti_file = input("Chemin vers le fichier NIfTI : ").strip()
        output_dir = input("Dossier de sortie : ").strip()
        
        if nifti_file and output_dir:
            exporter = NIfTIToPNGExporter(nifti_file, output_dir)
            exporter.export_all_slices()
        else:
            print("❌ Paramètres manquants")
    else:
        # Mode ligne de commande
        main()
